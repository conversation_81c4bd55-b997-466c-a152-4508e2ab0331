# 会议资料上传组件修改说明

## 修改概述
将会议资料页面的文件地址字段从普通输入框改为文件上传组件，并限制只能上传PDF文件。

## 修改的文件

### 1. frontend/src/option/materials/materials.js
**修改内容：**
- 将 `fileUrl` 字段的类型从 `input` 改为 `upload`
- 添加了上传组件的相关配置

**具体配置：**
```javascript
{
  label: "文件上传",
  prop: "fileUrl",
  type: "upload",
  listType: "text",
  dataType: "string",
  multiple: false,
  limit: 1,
  action: "/blade-resource/oss/endpoint/put-file",
  propsHttp: {
    res: "data",
    url: "link",
  },
  tip: "请上传PDF格式文件，文件大小不超过50MB",
  accept: ".pdf",
  span: 24,
  tableDisplay: false,
}
```

### 2. frontend/src/views/materials/materials.vue
**修改内容：**
- 在 `avue-crud` 组件中添加了 `:upload-after` 事件处理
- 添加了 `uploadAfter` 方法用于文件上传成功后的处理

**新增方法：**

#### uploadAfter 方法
- 处理上传成功后的响应
- 自动将上传成功的文件链接填充到 `fileUrl` 字段
- 显示上传成功的提示信息
- 让avue-crud组件自己处理文件回显

## 功能特性

### 文件类型限制
- 只允许上传PDF格式文件
- 通过 `accept=".pdf"` 属性限制文件选择

### 文件大小限制
- 建议文件大小不超过50MB
- 由后端服务器进行大小验证

### 用户体验
- 提供清晰的提示信息
- 上传过程中显示进度
- 上传成功后自动填充文件地址
- 在表格中隐藏上传组件显示（`tableDisplay: false`）

### 上传配置
- 使用系统统一的文件上传接口：`/blade-resource/oss/endpoint/put-file`
- 单文件上传模式（`multiple: false, limit: 1`）
- 文本列表显示模式（`listType: "text"`）

## 使用说明
1. 在添加或编辑会议资料时，点击"文件上传"字段
2. 选择PDF文件（系统会自动过滤非PDF文件）
3. 系统会验证文件类型和大小
4. 上传成功后，文件地址会自动填充到相应字段
5. 保存记录即可完成会议资料的添加

## 注意事项
- 确保上传的文件为PDF格式
- 文件大小不要超过50MB
- 上传过程中请勿关闭页面或刷新浏览器
